"use client";

import { useRef, useState, useEffect } from 'react';
import SectionTitleAnimation from './SectionTitleAnimation';
import ProcessCard from './ProcessCard';

const Process = () => {
  const sectionRef = useRef(null);
  const [processScrollProgress, setProcessScrollProgress] = useState(0);

  // Custom scroll handling like Projects section
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const processRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Calculate how far we've scrolled into the Process section
      const processTop = processRect.top;
      const processHeight = processRect.height;

      // Start process animations when Process section reaches top of viewport
      const triggerOffset = windowHeight * 0.1;
      if (processTop <= triggerOffset) {
        // Calculate scroll progress through Process section
        const scrolledIntoProcess = Math.abs(processTop - triggerOffset);
        const processAnimationDistance = processHeight * 0.8; // Use 80% for animations
        const rawScrollProgress = Math.min(1, scrolledIntoProcess / processAnimationDistance);

        setProcessScrollProgress(rawScrollProgress);
      } else {
        // Reset when Process section hasn't reached the trigger point yet
        setProcessScrollProgress(0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Process steps data
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  // Use a reasonable section height - not too long
  const sectionHeight = '200vh'; // Enough scroll distance for smooth card transitions

  return (
    <>
      {/* Reusable Section Title Animation */}
      <SectionTitleAnimation
        title="Process"
        currentSectionRef={sectionRef}
        previousSectionSelector='[data-section="projects"]'
        zIndex="z-0"
        className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
      />

      {/* Process Section - Dynamic height calculated to stop when last card is centered */}
      <section
        ref={sectionRef}
        data-section="process"
        className="bg-background relative z-10"
        style={{ height: sectionHeight }} // Fixed height to ensure last card reaches center
      >
        {/* Process Cards Container */}
        <div className="sticky top-0 h-screen w-full overflow-hidden">
          <div className="h-full flex items-center justify-center relative bg-background">
            <ProcessCard
              processSteps={processSteps}
              scrollProgress={processScrollProgress}
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;
