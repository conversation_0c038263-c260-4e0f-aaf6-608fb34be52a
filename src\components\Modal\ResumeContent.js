"use client";

import React from 'react';
import Button from '../Button';

const ResumeContent = () => {
  const handleDownloadPDF = () => {
    // TODO: Implement PDF download functionality
    console.log('Download PDF clicked');
  };

  return (
    <div className="p-8 pr-16">
      {/* Header with Picture Space */}
      <div className="mb-6 relative">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-3xl font-heading font-bold text-secondary mb-2">
              <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
            </h1>
            <p className="text-xl text-secondary/80 mb-4">
              Multimedia Artist & Creative Professional
            </p>

            {/* Contact Details */}
            <div className="mb-4 space-y-2">
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-secondary/60" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
                <a
                  href="http://linkedin.com/in/teodor-cretu"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-secondary/80 hover:text-accent transition-colors duration-200 text-sm"
                >
                  linkedin.com/in/teodor-cretu
                </a>
              </div>

              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-secondary/60" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6.5 12C5.11 12 4 13.11 4 14.5S5.11 17 6.5 17 9 15.89 9 14.5 7.89 12 6.5 12M17.5 12C16.11 12 15 13.11 15 14.5S16.11 17 17.5 17 20 15.89 20 14.5 18.89 12 17.5 12M12 7C10.61 7 9.5 8.11 9.5 9.5S10.61 12 12 12 14.5 10.89 14.5 9.5 13.39 7 12 7M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2M12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20Z"/>
                </svg>
                <a
                  href="http://www.be.net/teodorcretu"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-secondary/80 hover:text-accent transition-colors duration-200 text-sm"
                >
                  be.net/teodorcretu
                </a>
              </div>

              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-secondary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  className="text-secondary/80 hover:text-accent transition-colors duration-200 text-sm"
                >
                  <EMAIL>
                </a>
              </div>
            </div>

            <Button
              onClick={handleDownloadPDF}
              variant="filled"
              size="small"
            >
              Download PDF
            </Button>
          </div>

          {/* Picture placeholder - will be styled as background with fade */}
          <div className="w-32 h-32 ml-6 rounded-lg bg-secondary/5 border border-secondary/10 flex items-center justify-center">
            <span className="text-secondary/40 text-sm">Photo</span>
            {/* Future: Background image with fade effect will go here */}
          </div>
        </div>
      </div>

      {/* Professional Summary */}
      <div className="mb-8">
        <p className="text-secondary/80 leading-relaxed">
          I'm a versatile multimedia artist with experience across digital entertainment, marketing, and creative production. My work blends graphic design, 3D rendering, branding, and motion graphics, supported by an efficient, adaptable workflow. I move seamlessly between tools and disciplines to deliver production-ready assets with speed and precision. Guided by curiosity and continuous learning, I've also explored game design and interactive media to expand my creative range.
        </p>
      </div>

      {/* Resume Content */}
      <div className="space-y-8">
        {/* Experience Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Experience
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary">Video & Animation Designer</h3>
              <p className="text-secondary/80 font-medium">vidaXL.com</p>
              <p className="text-secondary/70 text-sm mb-2">November 2022 - Present • Bucharest</p>
              <p className="text-secondary/70 text-sm">
                vidaXL is an international online retailer, with headquarters in Venlo, The Netherlands.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 font-medium">vidaXL.com</p>
              <p className="text-secondary/70 text-sm">September 2021 - October 2022 • Bucharest</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 font-medium">Sound Design Theatre Company</p>
              <p className="text-secondary/70 text-sm mb-2">September 2019 - January 2021</p>
              <p className="text-secondary/70 text-sm">
                Sound Design Theatre Company focuses on the production of musical shows in Romania.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 font-medium">Newton Coin Project</p>
              <p className="text-secondary/70 text-sm mb-2">June 2018 - December 2018 • Remote</p>
              <p className="text-secondary/70 text-sm">
                Cryptocurrency created to help fund research in the medical and renewable energy fields.
              </p>
            </div>
          </div>
        </section>

        {/* Education Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Education
          </h2>
          <div>
            <h3 className="text-lg font-semibold text-secondary">Animation & Game Design</h3>
            <p className="text-secondary/80 font-medium">SAE Institute (Media Academy)</p>
            <p className="text-secondary/70 text-sm mb-3">October 2016 - September 2017 • Bucharest</p>
            <ul className="text-secondary/70 space-y-1 text-sm">
              <li>• Game design theory, level design, and character design</li>
              <li>• Classic and digital drawing fundamentals</li>
              <li>• 3D software proficiency: 3ds Max, Autodesk Maya, ZBrush</li>
              <li>• Procedural texturing, digital environment design, and animation basics</li>
            </ul>
          </div>
        </section>

        {/* Skills Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Skills
          </h2>
          <div className="space-y-6">
            {/* Tech Stack */}
            <div>
              <h3 className="text-lg font-semibold text-secondary mb-3">Tech Stack</h3>

              {/* Graphic & Motion */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-secondary/70 mb-2">Graphic & Motion</h4>
                <div className="flex flex-wrap gap-3 justify-start">
                  {[
                    {
                      name: 'Adobe After Effects',
                      icon: '/Creative Software Icons/After Effects-512.png',
                      alt: 'Adobe After Effects'
                    },
                    {
                      name: 'Adobe Photoshop',
                      icon: '/Creative Software Icons/Photoshop-512.png',
                      alt: 'Adobe Photoshop'
                    },
                    {
                      name: 'Adobe Lightroom',
                      icon: '/Creative Software Icons/Lightroom-512.png',
                      alt: 'Adobe Lightroom'
                    },
                    {
                      name: 'Adobe Illustrator',
                      icon: '/Creative Software Icons/Illustrator-512.png',
                      alt: 'Adobe Illustrator'
                    },
                    {
                      name: 'Adobe InDesign',
                      icon: '/Creative Software Icons/InDesign-512.png',
                      alt: 'Adobe InDesign'
                    }
                  ].map((software, index) => (
                    <div key={index} className="flex items-center justify-center">
                      <img
                        src={software.icon}
                        alt={software.alt}
                        className="w-8 h-8 object-contain"
                        title={software.name}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* 3D */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-secondary/70 mb-2">3D</h4>
                <div className="flex flex-wrap gap-3 justify-start">
                  {[
                    {
                      name: 'Blender',
                      icon: '/Creative Software Icons/blender-icon.svg',
                      alt: 'Blender'
                    },
                    {
                      name: 'Unreal Engine 5',
                      iconLight: '/Creative Software Icons/UE-Icon-2023-Black.svg',
                      iconDark: '/Creative Software Icons/UE-Icon-2023-White.svg',
                      alt: 'Unreal Engine 5'
                    }
                  ].map((software, index) => (
                    <div key={index} className="flex items-center justify-center">
                      {software.icon ? (
                        <img
                          src={software.icon}
                          alt={software.alt}
                          className="w-8 h-8 object-contain"
                          title={software.name}
                        />
                      ) : (
                        <>
                          <img
                            src={software.iconLight}
                            alt={software.alt}
                            className="w-8 h-8 object-contain [html[data-theme='light']_&]:block [html[data-theme='dark']_&]:hidden"
                            title={software.name}
                          />
                          <img
                            src={software.iconDark}
                            alt={software.alt}
                            className="w-8 h-8 object-contain [html[data-theme='light']_&]:hidden [html[data-theme='dark']_&]:block"
                            title={software.name}
                          />
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Code */}
              <div>
                <h4 className="text-sm font-medium text-secondary/70 mb-2">Code</h4>
                <div className="flex flex-wrap gap-3 justify-start">
                  {[
                    {
                      name: 'VS Code',
                      icon: '/Creative Software Icons/vscode.svg',
                      alt: 'Visual Studio Code'
                    },
                    {
                      name: 'GPT (AI)',
                      iconLight: '/Creative Software Icons/OpenAI-black-monoblossom.svg',
                      iconDark: '/Creative Software Icons/OpenAI-white-monoblossom.svg',
                      alt: 'OpenAI GPT'
                    }
                  ].map((software, index) => (
                    <div key={index} className="flex items-center justify-center">
                      {software.icon ? (
                        <img
                          src={software.icon}
                          alt={software.alt}
                          className="w-8 h-8 object-contain"
                          title={software.name}
                        />
                      ) : (
                        <>
                          <img
                            src={software.iconLight}
                            alt={software.alt}
                            className="w-8 h-8 object-contain [html[data-theme='light']_&]:block [html[data-theme='dark']_&]:hidden"
                            title={software.name}
                          />
                          <img
                            src={software.iconDark}
                            alt={software.alt}
                            className="w-8 h-8 object-contain [html[data-theme='light']_&]:hidden [html[data-theme='dark']_&]:block"
                            title={software.name}
                          />
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Professional Skills & Languages */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-secondary mb-3">Professional Skills</h3>
                <ul className="text-secondary/80 space-y-1">
                  <li>• Collaboration</li>
                  <li>• Adaptability</li>
                  <li>• Problem-Solving</li>
                  <li>• Communication</li>
                  <li>• Organization</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-secondary mb-3">Languages</h3>
                <ul className="text-secondary/80 space-y-1">
                  <li>• English (Full Professional Proficiency)</li>
                  <li>• Romanian (Native)</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Interests Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Interests
          </h2>
          <div className="flex flex-wrap gap-3">
            {[
              'Graphic Design',
              'Music Composition',
              'Animation',
              'Scriptwriting',
              'Level Design'
            ].map((interest, index) => (
              <span
                key={index}
                className="bg-accent/10 px-3 py-1 rounded-full text-sm font-medium [html[data-theme='light']_&]:text-secondary [html[data-theme='dark']_&]:text-accent"
              >
                {interest}
              </span>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default ResumeContent;
