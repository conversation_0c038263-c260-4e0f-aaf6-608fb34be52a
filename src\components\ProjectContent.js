"use client";

import { useModal } from '../contexts/ModalContext';

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  const { openModal } = useModal();

  const handleProjectClick = () => {
    openModal('project-details', project);
  };
  return (
    <div
      className="absolute inset-0 w-full h-full cursor-pointer"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
        // Blur effect during mask transition
        filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
      }}
      onClick={handleProjectClick}
    >
      {/* Full background image */}
      <div className="w-full h-full rounded-3xl overflow-hidden">
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-primary/20 flex items-center justify-center">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectContent;
