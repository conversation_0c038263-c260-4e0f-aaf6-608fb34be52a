"use client";

import { motion } from 'framer-motion';
import Button from './Button';
import { useTheme } from '../contexts/ThemeContext';
import { useModal } from '../contexts/ModalContext';

const ProjectText = ({ scrollProgress, projects }) => {
  const projectCount = projects.length;
  const { theme } = useTheme();
  const { openModal } = useModal();
  
  // Calculate which project should be visible
  const segmentSize = 1 / projectCount;
  let activeProjectIndex = -1;
  let activeOpacity = 0;

  // Find the currently active project
  for (let index = 0; index < projectCount; index++) {
    const segmentStart = index * segmentSize;
    const segmentEnd = (index + 1) * segmentSize;

    let opacity = 0;

    // Fast fade-in: text appears quickly when card is 50% in
    if (scrollProgress >= segmentStart + (segmentSize * 0.5) && scrollProgress < segmentEnd) {
      // Quick fade in over just 10% of segment (from 50% to 60%)
      const fadeInStart = segmentStart + (segmentSize * 0.5);
      const fadeInEnd = segmentStart + (segmentSize * 0.9);

      if (scrollProgress <= fadeInEnd) {
        const fadeInProgress = (scrollProgress - fadeInStart) / (segmentSize * 0.3);
        opacity = Math.min(1, fadeInProgress);
      } else {
        // Stay at full opacity from 60% to 100% of segment
        opacity = 1;
      }
    }
    // Fast fade-out: text disappears quickly when next card starts
    else if (scrollProgress >= segmentEnd && index < projectCount - 1) {
      // Quick fade out over just 10% of next segment (from 0% to 10% of next)
      const fadeOutStart = segmentEnd + (segmentSize * 0.3);
      // Fade out ends at 30% of next segment
      const fadeOutEnd = segmentEnd + (segmentSize * 0.5);

      if (scrollProgress <= fadeOutEnd) {
        const fadeOutProgress = (scrollProgress - fadeOutStart) / (segmentSize * 0.3);
        opacity = Math.max(0, 1 - fadeOutProgress);
      } else {
        opacity = 0;
      }
    }
    // For the last project, stay visible once it appears
    else if (index === projectCount - 1 && scrollProgress >= segmentStart + (segmentSize * 0.5)) {
      const fadeInStart = segmentStart + (segmentSize * 0.5);
      const fadeInEnd = segmentStart + (segmentSize * 0.6);

      if (scrollProgress <= fadeInEnd) {
        const fadeInProgress = (scrollProgress - fadeInStart) / (segmentSize * 0.1);
        opacity = Math.min(1, fadeInProgress);
      } else {
        opacity = 1;
      }
    }
    // Handle completion state - when scrollProgress >= 1.0, keep last project visible
    else if (scrollProgress >= 1.0 && index === projectCount - 1) {
      opacity = 1;
    }

    // Set active project if this one has the highest opacity
    if (opacity > activeOpacity) {
      activeProjectIndex = index;
      activeOpacity = opacity;
    }
  }

  const activeProject = activeProjectIndex >= 0 ? projects[activeProjectIndex] : null;

  return (
    <div className="h-full flex flex-col justify-center px-8">
      {activeProject && (
        <div
          className="w-full h-full flex flex-col px-2 justify-center"
          style={{ opacity: activeOpacity }}
        >
          {/* Project Title */}
          <h3 className="font-heading font-bold text-3xl text-secondary mb-4">
            {activeProject.title}
          </h3>

          {/* Project Description */}
          <p className="text-secondary text-lg leading-relaxed mb-6">
            {activeProject.description}
          </p>

          {/* Tech Stack */}
          <div className="mb-6">
            <h4 className="text-secondary font-semibold mb-3">Tech Stack</h4>
            <div className="flex flex-wrap gap-3">
              {activeProject.techStack.map((tech) => {
                const getIconPath = (techName) => {
                  const iconMap = {
                    'PS': '/Creative Software Icons/Photoshop-512.png',
                    'AI': '/Creative Software Icons/Illustrator-512.png',
                    'VS Code': '/Creative Software Icons/vscode.svg',
                    'GPT': theme === 'dark'
                      ? '/Creative Software Icons/OpenAI-white-monoblossom.svg'
                      : '/Creative Software Icons/OpenAI-black-monoblossom.svg'
                  };
                  return iconMap[techName] || null;
                };

                const iconPath = getIconPath(tech);

                return iconPath ? (
                  <div key={tech} className="w-8 h-8 flex items-center justify-center">
                    <img
                      src={iconPath}
                      alt={tech}
                      className="w-full h-full object-contain"
                    />
                  </div>
                ) : (
                  <span
                    key={tech}
                    className="bg-primary border border-secondary/30 text-secondary px-3 py-1 rounded-lg text-sm"
                  >
                    {tech}
                  </span>
                );
              })}
            </div>
          </div>

          {/* Project Links */}
          <div className="flex gap-4">
            <Button
              variant="filled"
              onClick={() => openModal('project-details', activeProject)}
            >
              View More
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectText;
