"use client";

import React from 'react';
import { useModal } from '../../contexts/ModalContext';
import ResumeContent from './ResumeContent';
import ProjectWizard from './ProjectWizard';

const ModalContent = () => {
  const { modalType, modalData } = useModal();

  // Render different content based on modal type
  switch (modalType) {
    case 'resume':
      return <ResumeContent />;
    
    case 'project-wizard':
      return <ProjectWizard />;
    
    case 'project-details':
      return (
        <div className="p-8 pr-16">
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4">
            Project Details
          </h2>
          <p className="text-secondary/80">
            Project details content will go here. Data: {JSON.stringify(modalData)}
          </p>
        </div>
      );

    default:
      return (
        <div className="p-8 pr-16">
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4">
            Modal Content
          </h2>
          <p className="text-secondary/80">
            Unknown modal type: {modalType}
          </p>
        </div>
      );
  }
};

export default ModalContent;
