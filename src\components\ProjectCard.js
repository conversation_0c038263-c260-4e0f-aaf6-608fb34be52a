"use client";

import ProjectContent from './ProjectContent';

const ProjectCard = ({ scrollProgress, projects }) => {
  // Calculate which project we're currently showing
  const projectCount = projects.length;
  const segmentSize = 1 / projectCount;

  // Handle completion state - when scrollProgress is 1.0, show the last project fully
  let currentProjectIndex, segmentProgress;

  if (scrollProgress >= 1.0) {
    // Animation complete - show last project at final position
    currentProjectIndex = projectCount - 1;
    segmentProgress = 1.0;
  } else {
    currentProjectIndex = Math.floor(scrollProgress / segmentSize);
    segmentProgress = (scrollProgress % segmentSize) / segmentSize; // 0-1 within current segment

    // Ensure we don't exceed array bounds
    currentProjectIndex = Math.min(currentProjectIndex, projectCount - 1);
  }

  const activeProjectIndex = currentProjectIndex;

  // First card slides in from right (like before)
  let cardOpacity = 0;
  let cardTranslateX = 200;

  if (activeProjectIndex >= 0) {
    cardOpacity = 1;
    // Only the first project triggers the card slide-in
    if (activeProjectIndex === 0) {
      cardTranslateX = Math.max(0, 200 - (segmentProgress * 200));
    } else {
      cardTranslateX = 0; // Card stays in position for subsequent projects
    }
  }

  return (
    <div className="w-full h-full relative bg-background flex items-center justify-center p-4">
      {/* Single card frame that slides in once */}
      <div
        className="absolute rounded-3xl shadow-lg overflow-hidden"
        style={{
          opacity: cardOpacity,
          transform: `translateX(${cardTranslateX}%)`,
          zIndex: 1,
          // Responsive sizing within 90% container: use as much space as possible while maintaining 4:3 aspect ratio
          width: 'min(100%, calc((100vh - 8rem) * 4/3))', // Width constrained by height
          height: 'min(calc(100vw * 0.9 * 0.67 * 3/4), calc(100vh - 8rem))', // Height constrained by container width (90% * 2/3)
          aspectRatio: '4/3'
        }}
      >
        {/* Render all project content inside the single frame */}
        {projects.map((project, index) => {
          let contentOpacity = 0;
          let maskProgress = 0;
          let blurAmount = 0;

          // Clean, simple logic for each project
          const segmentStart = index * segmentSize;
          const segmentEnd = (index + 1) * segmentSize;

          // Default state
          contentOpacity = 0;
          maskProgress = 0;
          blurAmount = 0;

          // Check if this project should be visible
          if (scrollProgress >= segmentStart + (segmentSize * 0.3)) {
            // Project becomes visible at 30% of its segment
            contentOpacity = 1;

            // For first project (index 0), no mask needed
            if (index === 0) {
              maskProgress = 1;
              blurAmount = 0; // No blur for first project
            }
            // For other projects, handle mask transition starting from when project becomes visible
            else {
              const maskStart = segmentStart + (segmentSize * 0.3); // Start earlier at 30%
              const maskEnd = segmentStart + (segmentSize * 0.8);   // End at 80% (unchanged)

              if (scrollProgress >= maskStart && scrollProgress <= maskEnd) {
                // During mask transition - starts from 0 when project first appears
                const progress = (scrollProgress - maskStart) / (maskEnd - maskStart);
                maskProgress = Math.min(1, Math.max(0, progress));

                // Blur effect: starts strong (8px) and reduces to 0 as mask reveals
                blurAmount = 8 * (1 - progress); // 8px blur at start, 0px at end
              } else if (scrollProgress > maskEnd) {
                // After mask transition completes
                maskProgress = 1;
                blurAmount = 0; // No blur when fully revealed
              } else {
                // Before mask starts (shouldn't happen since contentOpacity check, but safety)
                maskProgress = 0;
                blurAmount = 8; // Full blur before reveal starts
              }
            }
          }

          // Handle when project should disappear (only for non-last projects)
          if (index < projectCount - 1) {
            const nextSegmentStart = segmentEnd;
            const disappearPoint = nextSegmentStart + (segmentSize * 0.8); // Disappear when next project's mask completes

            if (scrollProgress > disappearPoint) {
              contentOpacity = 0;
              maskProgress = 0;
            }
          }

          // Handle completion state for last project
          if (scrollProgress >= 1.0 && index === projectCount - 1) {
            contentOpacity = 1;
            maskProgress = 1;
          }

          return (
            <ProjectContent
              key={project.id}
              project={project}
              projects={projects}
              activeProjectIndex={activeProjectIndex}
              opacity={contentOpacity}
              maskProgress={maskProgress}
              blurAmount={blurAmount}
            />
          );
        })}
      </div>
    </div>
  );
};

export default ProjectCard;
