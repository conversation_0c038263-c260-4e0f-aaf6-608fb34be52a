"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Calculate which card should be visible based on scroll progress
  const cardCount = processSteps.length;
  const segmentSize = 1 / cardCount; // Each card gets equal scroll time

  // Determine active card index
  let activeCardIndex = -1;
  if (scrollProgress > 0) {
    activeCardIndex = Math.floor(scrollProgress / segmentSize);
    // Ensure we don't exceed array bounds
    activeCardIndex = Math.min(activeCardIndex, cardCount - 1);
  }

  return (
    <div className="w-full h-full flex items-center justify-center relative">
      {/* Single card container - cards appear in the same position */}
      <div className="relative">
        {processSteps.map((step, index) => {
          // Calculate opacity and scale for each card
          let cardOpacity = 0;
          let cardScale = 0.8;

          // Show card when it's the active one
          if (index === activeCardIndex) {
            cardOpacity = 1;
            cardScale = 1;
          }

          return (
            <motion.div
              key={step.id}
              className="absolute bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              style={{
                width: '280px',
                height: '320px',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                opacity: cardOpacity,
                scale: cardScale,
                zIndex: index === activeCardIndex ? 10 : 1
              }}
              animate={{
                opacity: cardOpacity,
                scale: cardScale
              }}
              transition={{
                duration: 0.3,
                ease: "easeOut"
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
