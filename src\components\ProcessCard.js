"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Calculate which card should be visible based on scroll progress
  const cardCount = processSteps.length;

  // Give each card time to stay visible with pauses between transitions
  let activeCardIndex = -1;
  if (scrollProgress > 0.05 && scrollProgress < 0.85) {
    // Card timing with gaps:
    // Card 1: 0.05-0.20 (visible), 0.20-0.25 (fade out pause)
    // Card 2: 0.25-0.40 (visible), 0.40-0.45 (fade out pause)
    // Card 3: 0.45-0.60 (visible), 0.60-0.65 (fade out pause)
    // Card 4: 0.65-0.80 (visible), 0.80-0.85 (fade out pause)

    if (scrollProgress >= 0.05 && scrollProgress < 0.20) {
      activeCardIndex = 0;
    } else if (scrollProgress >= 0.25 && scrollProgress < 0.40) {
      activeCardIndex = 1;
    } else if (scrollProgress >= 0.45 && scrollProgress < 0.60) {
      activeCardIndex = 2;
    } else if (scrollProgress >= 0.65 && scrollProgress < 0.80) {
      activeCardIndex = 3;
    }
    // During pause periods (0.20-0.25, 0.40-0.45, 0.60-0.65, 0.80-0.85), no card is active
  }

  // Debug logging
  console.log('Process scroll:', scrollProgress.toFixed(3), 'Active card:', activeCardIndex);

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Cards container - all cards in the same centered position */}
      <div className="relative w-[280px] h-[320px]">
        {processSteps.map((step, index) => {
          // Calculate opacity and scale for each card
          let cardOpacity = 0;
          let cardScale = 0.8;

          // Show card when it's the active one
          if (index === activeCardIndex) {
            cardOpacity = 1;
            cardScale = 1;
          }

          return (
            <motion.div
              key={step.id}
              className="absolute inset-0 bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              animate={{
                opacity: cardOpacity,
                scale: cardScale
              }}
              transition={{
                duration: 0.3,
                ease: "easeInOut"
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
