"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Calculate which card should be visible based on scroll progress
  const cardCount = processSteps.length;

  // Only show cards when we're properly in the section (not during transition)
  let activeCardIndex = -1;
  if (scrollProgress > 0.1 && scrollProgress < 0.9) {
    // Use the middle 80% of scroll progress for card transitions
    const adjustedProgress = (scrollProgress - 0.1) / 0.8; // Normalize to 0-1
    activeCardIndex = Math.floor(adjustedProgress * cardCount);
    // Ensure we don't exceed array bounds
    activeCardIndex = Math.min(activeCardIndex, cardCount - 1);
    activeCardIndex = Math.max(activeCardIndex, 0);
  }

  // Temporary debug logging
  if (scrollProgress > 0) {
    console.log('Process scroll:', scrollProgress.toFixed(3), 'Active card:', activeCardIndex);
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Cards container - all cards in the same centered position */}
      <div className="relative w-[280px] h-[320px]">
        {processSteps.map((step, index) => {
          // Calculate opacity and scale for each card
          let cardOpacity = 0;
          let cardScale = 0.8;

          // Only show card when it's the active one (no fallback)
          if (index === activeCardIndex) {
            cardOpacity = 1;
            cardScale = 1;
          }

          return (
            <motion.div
              key={step.id}
              className="absolute inset-0 bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              animate={{
                opacity: cardOpacity,
                scale: cardScale
              }}
              transition={{
                duration: 0.3,
                ease: "easeOut"
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
