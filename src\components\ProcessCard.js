"use client";

import { motion, useTransform } from 'framer-motion';
import { useState, useEffect } from 'react';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Track window width for responsive calculations
  const [windowWidth, setWindowWidth] = useState(1200); // Default fallback

  useEffect(() => {
    const updateWindowWidth = () => setWindowWidth(window.innerWidth);
    updateWindowWidth(); // Set initial value
    window.addEventListener('resize', updateWindowWidth);
    return () => window.removeEventListener('resize', updateWindowWidth);
  }, []);

  // Calculate stopping position so last card reaches center
  const cardSpacing = 350; // pixels between cards
  const cardWidth = 280; // Card width from the style
  const lastCardIndex = processSteps.length - 1;
  const lastCardPosition = lastCardIndex * cardSpacing; // Position of last card's left edge within wrapper

  // To center the last card: 50vw (screen center) - lastCardPosition - half card width
  const startPosition = windowWidth; // 100vw in pixels
  const stopPosition = (windowWidth * 0.5) - lastCardPosition - (cardWidth * 0.5); // 50vw - lastCardPosition - cardWidth/2

  // Simple wrapper movement - move the entire container with natural scroll speed
  // Complete animation at 70% so there's still 30% of section left for normal scrolling
  const wrapperTranslateX = useTransform(
    scrollProgress,
    [0.2, 0.65], // Start moving after 15% delay, complete at 70% (faster movement)
    [startPosition, stopPosition], // Start completely off-screen right, stop when last card is centered
    {
      ease: (t) => t, // Linear easing - no smoothing, pure 1:1 scroll mapping
      clamp: true // Enable clamping to ensure it stops exactly at the end values
    }
  );

  return (
    <div className="w-full h-full flex items-center justify-center relative overflow-hidden">
      {/* Feathered edge masks - removed pointer-events-none to allow text selection */}
      <div
        className="absolute inset-0 z-20"
        style={{
          maskImage: 'linear-gradient(to right, transparent 0%, black 50%, black 75%, transparent 100%)',
          WebkitMaskImage: 'linear-gradient(to right, transparent 0%, black 25%, black 75%, transparent 100%)'
        }}
      >
        {/* Cards wrapper that moves as a unit */}
        <motion.div
          className="relative h-full flex items-center bg-primary"
          style={{
            x: wrapperTranslateX
          }}
        >
          {processSteps.map((step, index) => {
            const leftPosition = index * cardSpacing;

            return (
              <div
                key={step.id}
                className="absolute bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
                style={{
                  width: '280px',
                  height: '320px',
                  left: `${leftPosition}px`,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  zIndex: 100 - index
                }}
              >
                {/* Card Content */}
                <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                  {/* Step Number */}
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                    <span className="text-primary text-xl font-bold font-heading">
                      {step.number}
                    </span>
                  </div>

                  {/* Step Title */}
                  <h3 className="text-secondary font-heading font-bold text-xl">
                    {step.title}
                  </h3>

                  {/* Step Description */}
                  <p className="text-secondary/80 text-sm leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </motion.div>
      </div>
    </div>
  );
};

export default ProcessCard;
