"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Calculate which card should be visible based on scroll progress
  const cardCount = processSteps.length;

  // Give each card more time to stay visible
  let activeCardIndex = -1;
  if (scrollProgress > 0.05 && scrollProgress < 0.85) {
    // Use only 80% of scroll progress for card transitions (more dwell time)
    // Each card gets: 0.05-0.25, 0.25-0.45, 0.45-0.65, 0.65-0.85
    const adjustedProgress = (scrollProgress - 0.05) / 0.8; // Normalize to 0-1
    activeCardIndex = Math.floor(adjustedProgress * cardCount);
    // Ensure we don't exceed array bounds
    activeCardIndex = Math.min(activeCardIndex, cardCount - 1);
    activeCardIndex = Math.max(activeCardIndex, 0);
  }

  // Debug logging
  console.log('Process scroll:', scrollProgress.toFixed(3), 'Active card:', activeCardIndex);

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Cards container - all cards in the same centered position */}
      <div className="relative w-[280px] h-[320px]">
        {processSteps.map((step, index) => {
          // Calculate opacity and scale for each card
          let cardOpacity = 0;
          let cardScale = 0.8;

          // Show card when it's the active one
          if (index === activeCardIndex) {
            cardOpacity = 1;
            cardScale = 1;
          }

          return (
            <motion.div
              key={step.id}
              className="absolute inset-0 bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{
                opacity: cardOpacity,
                scale: cardScale
              }}
              transition={{
                duration: 0.4,
                ease: [0.25, 0.1, 0.25, 1], // Custom easing for smoother animation
                type: "spring",
                stiffness: 100,
                damping: 15
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
