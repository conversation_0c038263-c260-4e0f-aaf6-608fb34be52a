"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Calculate which card should be visible based on scroll progress
  const cardCount = processSteps.length;

  // Optimized dynamic card timing - automatically fits all cards
  const startDelay = 0.15;       // When first card appears
  const endBuffer = 0.15;        // Buffer before section ends
  const pauseBetweenCards = 0.03; // Small pause between cards

  // Calculate available scroll space and distribute it among cards
  const availableSpace = 1.0 - startDelay - endBuffer; // Total space for all cards
  const totalPauses = (cardCount - 1) * pauseBetweenCards; // Space needed for pauses
  const spaceForCards = availableSpace - totalPauses; // Space left for actual cards
  const cardDuration = spaceForCards / cardCount; // Each card gets equal time

  // Calculate which card should be active
  let activeCardIndex = -1;

  if (scrollProgress >= startDelay && scrollProgress <= (1 - endBuffer)) {
    const adjustedProgress = scrollProgress - startDelay;

    // Calculate cumulative timing for each card
    for (let i = 0; i < cardCount; i++) {
      const cardStart = i * (cardDuration + pauseBetweenCards);
      const cardEnd = cardStart + cardDuration;

      if (adjustedProgress >= cardStart && adjustedProgress < cardEnd) {
        activeCardIndex = i;
        break;
      }
    }
  }

  // Debug logging
  console.log('Process scroll:', scrollProgress.toFixed(3), 'Active card:', activeCardIndex);

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Cards container - all cards in the same centered position */}
      <div className="relative w-[280px] h-[320px]">
        {processSteps.map((step, index) => {
          // Calculate opacity and scale for each card
          let cardOpacity = 0;
          let cardScale = 0.8;

          // Show card when it's the active one
          if (index === activeCardIndex) {
            cardOpacity = 1;
            cardScale = 1;
          }

          return (
            <motion.div
              key={step.id}
              className="absolute inset-0 bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              animate={{
                opacity: cardOpacity,
                scale: cardScale
              }}
              transition={{
                duration: 0.2,
                ease: "easeInOut"
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
