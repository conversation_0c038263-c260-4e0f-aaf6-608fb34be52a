"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Calculate which card should be visible based on scroll progress
  const cardCount = processSteps.length;

  // Dynamic card timing - adjust these values to control all cards at once
  const startDelay = 0.3;        // When first card appears (0.1 = 10% into section)
  const cardDuration = 0.20;     // How long each card stays visible (0.15 = 15% of scroll)
  const pauseDuration = 0.05;    // Pause between cards (0.05 = 5% of scroll)
  const endBuffer = 0.1;         // Buffer before section ends (0.1 = 10% before end)

  // Calculate dynamic timing for any number of cards
  let activeCardIndex = -1;
  const totalCycleTime = cardDuration + pauseDuration; // Time for one card + pause

  if (scrollProgress > startDelay && scrollProgress < (1 - endBuffer)) {
    const adjustedProgress = scrollProgress - startDelay;

    // Find which card cycle we're in
    const currentCycle = Math.floor(adjustedProgress / totalCycleTime);
    const cycleProgress = adjustedProgress % totalCycleTime;

    // Only show card during the visible portion of its cycle (not during pause)
    if (cycleProgress < cardDuration && currentCycle < cardCount) {
      activeCardIndex = currentCycle;
    }
  }

  // Debug logging
  console.log('Process scroll:', scrollProgress.toFixed(3), 'Active card:', activeCardIndex);

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Cards container - all cards in the same centered position */}
      <div className="relative w-[280px] h-[320px]">
        {processSteps.map((step, index) => {
          // Calculate opacity and scale for each card
          let cardOpacity = 0;
          let cardScale = 0.8;

          // Show card when it's the active one
          if (index === activeCardIndex) {
            cardOpacity = 1;
            cardScale = 1;
          }

          return (
            <motion.div
              key={step.id}
              className="absolute inset-0 bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              animate={{
                opacity: cardOpacity,
                scale: cardScale
              }}
              transition={{
                duration: 0.2,
                ease: "easeInOut"
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
